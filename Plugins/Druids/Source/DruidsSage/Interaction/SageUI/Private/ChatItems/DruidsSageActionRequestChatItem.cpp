#include "ChatItems/DruidsSageActionRequestChatItem.h"

#include "DruidsSageMessagingHandler.h"
#include "Components/TextBlock.h"
#include "Components/Button.h"
#include "Components/ScrollBox.h"

UDruidsSageActionRequestChatItem::UDruidsSageActionRequestChatItem(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
	, RoleWidget(nullptr)
	, MessageWidget(nullptr)
	, ApplyButton(nullptr)
	, ContentJsonValue(nullptr)
{
}

void UDruidsSageActionRequestChatItem::NativePreConstruct()
{
	Super::NativePreConstruct();
	SetupWidgets();
}

void UDruidsSageActionRequestChatItem::NativeConstruct()
{
	Super::NativeConstruct();
	SetupWidgets();

	if (ApplyButton)
	{
		ApplyButton->OnClicked.AddDynamic(this, &UDruidsSageActionRequestChatItem::OnApplyButtonClicked);
	}
}

void UDruidsSageActionRequestChatItem::SynchronizeProperties()
{
	Super::SynchronizeProperties();
	SetupWidgets();
}

FName UDruidsSageActionRequestChatItem::GetTypeNameCpp() const
{
	return GetClassName();
}

void UDruidsSageActionRequestChatItem::FillInDruidsMessageCpp(FDruidsSageChatMessage& Message) const
{
	Message.SetRole(EDruidsSageChatRole::Assistant);
}

EDruidsSageChatRole UDruidsSageActionRequestChatItem::GetMessageRoleCpp() const
{
	return EDruidsSageChatRole::Assistant;
}

TWeakObjectPtr<UDruidsSageMessagingHandler> UDruidsSageActionRequestChatItem::GetMessagingHandlerCpp() const
{
	return MessagingHandler;
}

void UDruidsSageActionRequestChatItem::UpdateFromContentJsonCpp(const TSharedPtr<FJsonValue>& ContentJson)
{
	ContentJsonValue = ContentJson;

	TSharedPtr<FJsonObject>* JsonObject = nullptr;
	if (!ContentJson.IsValid() || !ContentJson->TryGetObject(JsonObject) || !JsonObject)
	{
		return;
	}

	FString Summary;
	if ((*JsonObject)->TryGetStringField(TEXT("summary"), Summary))
	{
		if (MessageWidget)
		{
			MessageWidget->SetText(FText::FromString(Summary));
		}
	}
}

void UDruidsSageActionRequestChatItem::InitializeActionRequestChatItem(const TScriptInterface<ISageExtensionDelegator>& InExtensionDelegator)
{
	ExtensionDelegator = InExtensionDelegator;
}

void UDruidsSageActionRequestChatItem::SetScrollBoxReference(UScrollBox* InScrollBox)
{
	MessagingHandler = NewObject<UDruidsSageMessagingHandler>();
	MessagingHandler->SetFlags(RF_Standalone);
	MessagingHandler->ScrollBoxReference = InScrollBox;
}

void UDruidsSageActionRequestChatItem::OnApplyButtonClicked()
{
	if (OnActionApplied.IsBound() && ContentJsonValue.IsValid())
	{
		// Convert TSharedPtr<FJsonValue> to FString for Blueprint compatibility
		FString OutputString;
		TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
		FJsonSerializer::Serialize(ContentJsonValue, TEXT(""), Writer);
		OnActionApplied.Broadcast(OutputString);
	}
}

void UDruidsSageActionRequestChatItem::SetupWidgets()
{
	if (RoleWidget)
	{
		RoleWidget->SetText(FText::FromString(TEXT("Action Request:")));
	}

	// Note: UButton doesn't have SetText method in UE 5.5
	// Button text should be set in the UMG Blueprint or through a child TextBlock
	// if (ApplyButton)
	// {
	//     ApplyButton->SetText(FText::FromString(TEXT("APPLY")));
	// }
}
