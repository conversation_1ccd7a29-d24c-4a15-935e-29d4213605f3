#pragma once

#include "CoreMinimal.h"

/**
 * SimpleJSON wrapper class that makes JSON manipulation as easy as JavaScript/Ruby
 * 
 * Key Design Principles:
 * - Each SimpleJSON object is in one of three states:
 *   a) Standalone FJsonValue (root or detached)
 *   b) Item in a parent JSON Array (has parent array + index)
 *   c) Item in a parent JSON Object (has parent object + key)
 * - Modifications update the parent directly when needed
 * - Clean operator overloading for natural syntax
 * 
 * Usage examples:
 * 
 * // Reading JSON
 * SimpleJSON json = SimpleJSON::Parse(jsonString);
 * FString name = json["player"]["name"].AsString();
 * int32 level = json["player"]["level"].AsInt();
 * 
 * // Writing JSON - modifications affect the original structure
 * SimpleJSON json;
 * json["player"]["name"] = "John";
 * json["player"]["level"] = 42;
 * json["items"][0] = "sword";
 * // All modifications propagate correctly!
 */

class SAGEJSONUTILITIES_API SimpleJSON: public TSharedFromThis<SimpleJSON>
{
private:
    // The actual JSON value
    TSharedPtr<FJsonValue> JsonValue;
    
    // Parent tracking - exactly one of these will be valid for non-root objects
    TSharedPtr<FJsonObject> ParentObject;  // If this is a field in an object
    FString ParentKey;                     // The key in the parent object
    
    TSharedPtr<FJsonValue> ParentArrayValue; // If this is an element in an array (holds the FJsonValueArray)
    int32 ArrayIndex;                         // The index in the parent array

    // Helper to determine our state
    bool IsStandalone() const { return !ParentObject.IsValid() && !ParentArrayValue.IsValid(); }
    bool IsObjectField() const { return ParentObject.IsValid(); }
    bool IsArrayElement() const { return ParentArrayValue.IsValid(); }

    // Constructors for creating child references
    SimpleJSON(const TSharedPtr<FJsonValue>& InValue, const TSharedPtr<FJsonObject>& InParentObject,
               const FString& InKey)
        : JsonValue(InValue), ParentObject(InParentObject), ParentKey(InKey), ArrayIndex(-1)
    {
        EnsureValid();
    }

    SimpleJSON(const TSharedPtr<FJsonValue>& InValue, const TSharedPtr<FJsonValue>& InParentArrayValue,
               const int32 InIndex)
        : JsonValue(InValue), ParentArrayValue(InParentArrayValue), ArrayIndex(InIndex)
    {
        EnsureValid();
    }

    //Ensure valid states
    void EnsureValid();
    void UpdateParent();
    void EnsureObject();
    void EnsureArray();

public:
    // Constructor 1: Default - create empty JSON object
    SimpleJSON() : ArrayIndex(-1)
    {
        JsonValue = MakeShareable(new FJsonValueObject(MakeShareable(new FJsonObject)));
    }

    // Constructor 2: From FJsonValue copy
    explicit SimpleJSON(const FJsonValue& InValue);

    // Constructor 3: From FJsonObject copy
    explicit SimpleJSON(const FJsonObject& InObject) : ArrayIndex(-1)
    {
        JsonValue = MakeShareable(new FJsonValueObject(MakeShareable(new FJsonObject(InObject))));
    }

    // Constructor 4: From TSharedPtr<FJsonValue> copy
    explicit SimpleJSON(const TSharedPtr<FJsonValue>& InValue) : JsonValue(InValue), ArrayIndex(-1)
    {
        EnsureValid();
    }

    // Constructor 5: From string parsing
    explicit SimpleJSON(const FString& JsonString);

    // Static factory methods
    static SimpleJSON Parse(const FString& JsonString);
    static SimpleJSON FromFile(const FString& FilePath);
    static SimpleJSON Array();
    static SimpleJSON Object();

    // Object property access operator
    SimpleJSON operator[](const FString& Key);

    // Array access operator
    SimpleJSON operator[](int32 Index);

    // Assignment operators for different types
    SimpleJSON& operator=(const FString& Value);
    SimpleJSON& operator=(const TCHAR* Value);
    SimpleJSON& operator=(int32 Value);
    SimpleJSON& operator=(float Value);
    SimpleJSON& operator=(double Value);
    SimpleJSON& operator=(bool Value);
    SimpleJSON& operator=(const TArray<SimpleJSON>& Array);
    SimpleJSON& operator=(const SimpleJSON& Other);

    SimpleJSON& SetNull();

    //Implicit type conversions
    explicit operator FString() const
    {
        return AsString();
    }

    explicit operator int32() const
    {
        return AsInt();
    }

    explicit operator float() const
    {
        return AsFloat();
    }

    explicit operator double() const
    {
        return AsDouble();
    }

    explicit operator bool() const
    {
        return AsBool();
    }

    explicit operator TArray<SimpleJSON>() const
    {
        return AsArray();
    }
    
    // Type checking methods
    bool IsString() const;
    bool IsNumber() const;
    bool IsBool() const;
    bool IsArray() const;
    bool IsObject() const;
    bool IsNull() const;
    bool IsValid() const;
    bool IsEmpty() const;

    // Value extraction methods
    FString AsString(const FString& DefaultValue = TEXT("")) const;
    int32 AsInt(int32 DefaultValue = 0) const;
    float AsFloat(float DefaultValue = 0.0f) const;
    double AsDouble(double DefaultValue = 0.0) const;
    bool AsBool(bool DefaultValue = false) const;
    TArray<SimpleJSON> AsArray() const;
    TArray<TSharedPtr<FJsonValue>> AsNativeArray() const;

    // Utility methods
    bool HasKey(const FString& Key) const;
    TArray<FString> GetKeys() const;
    bool RemoveKey(const FString& Key) const;
    int32 Size() const;

    //Add to array
    void Push(const SimpleJSON& Item);
    void Push(const FString& Item);
    void Push(const int32 Item);
    void Push(const float Item);
    void Push(const bool Item);

    // Helper for fluent interface
    SimpleJSON& SetValue(const FString& Value);
    SimpleJSON& SetValue(const int32 Value);
    SimpleJSON& SetValue(const float Value);
    SimpleJSON& SetValue(const bool Value);

    // Remove from array
    bool RemoveAt(const int32 Index) const;

    // Clear array or object
    void Clear() const;

    // Serialization
    FString ToString(const bool bPrettyPrint = false) const;
    bool SaveToFile(const FString& FilePath, bool bPrettyPrint = true) const;

    // Direct access to underlying JSON value
    TSharedPtr<FJsonValue> GetJsonValue() const { return JsonValue; }
    TSharedPtr<FJsonObject> GetJsonObject() const;
    
    // Iterator support
    class Iterator
    {
    private:
        bool bIsMapIterator;
        using MapIteratorType = TMap<FString, TSharedPtr<FJsonValue>>::TConstIterator;
        using ArrayIteratorType = TArray<TSharedPtr<FJsonValue>>::TConstIterator;
        TUniquePtr<MapIteratorType> MapIter;
        TUniquePtr<ArrayIteratorType> ArrayIter;

    public:
        Iterator(const TMap<FString, TSharedPtr<FJsonValue>>& Map, bool bBegin);
        Iterator(const TArray<TSharedPtr<FJsonValue>>& Array, bool bBegin);
        SimpleJSON operator*() const;
        Iterator& operator++();
        bool operator!=(const Iterator& Other) const;
    };

    Iterator begin() const;
    Iterator end() const;
};

#define JSON_PARSE(JsonString) SimpleJSON::Parse(JsonString)
#define JSON_FROM_FILE(FilePath) SimpleJSON::FromFile(FilePath)

// Example usage class to demonstrate the API
class SAGEJSONUTILITIES_API JsonExamples
{
public:
    static void ExampleUsage()
    {
        // Reading JSON - as easy as JavaScript!
        FString JsonString = TEXT(R"({
            "player": {
                "name": "John Doe",
                "level": 42,
                "active": true,
                "inventory": ["sword", "potion", "key"],
                "stats": {
                    "hp": 100,
                    "mp": 50
                }
            }
        })");

        SimpleJSON json = SimpleJSON::Parse(JsonString);
        
        // Simple property access
        FString playerName = json["player"]["name"].AsString();
        int32 playerLevel = json["player"]["level"].AsInt();
        bool isActive = json["player"]["active"].AsBool();
        
        // Array access
        TArray<SimpleJSON> inventory = json["player"]["inventory"].AsArray();
        for (const SimpleJSON& item : inventory)
        {
            UE_LOG(LogTemp, Log, TEXT("Item: %s"), *item.AsString());
        }
        
        // Creating JSON - clean and simple
        SimpleJSON newJson;
        newJson["character"]["name"] = TEXT("Jane Smith");
        newJson["character"]["level"] = 25;
        newJson["character"]["equipped"]["weapon"] = TEXT("bow");
        newJson["character"]["equipped"]["armor"] = TEXT("leather");
        
        // Array creation
        TArray<SimpleJSON> skills;
        SimpleJSON skill1, skill2;
        skill1 = TEXT("archery");
        skill2 = TEXT("stealth");
        newJson["character"]["skills"] = TArray<SimpleJSON>{skill1, skill2};
        
        // Output to string
        FString result = newJson.ToString(true); // Pretty printed
        UE_LOG(LogTemp, Log, TEXT("Generated JSON: %s"), *result);
        
        // Save to file
        newJson.SaveToFile(TEXT("character.json"));
    }
};
